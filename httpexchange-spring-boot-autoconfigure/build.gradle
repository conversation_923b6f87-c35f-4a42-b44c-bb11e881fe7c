dependencies {
    api("org.springframework.boot:spring-boot-autoconfigure")
    api("org.springframework:spring-web")
    compileOnly("org.springframework.cloud:spring-cloud-starter-loadbalancer:${springCloudCommonsVersion}")
    compileOnly("org.springframework.boot:spring-boot-starter-webclient")
    compileOnly("org.springframework.boot:spring-boot-starter-restclient")

    compileOnly("com.github.spotbugs:spotbugs-annotations:${spotbugsAnnotationsVersion}")

    compileOnly("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")

    // dynamic refresh configuration for exchange clients
    compileOnly("org.springframework.cloud:spring-cloud-context:${springCloudCommonsVersion}")
    // support @SpringQueryMap
    compileOnly("org.springframework.cloud:spring-cloud-openfeign-core:${springCloudOpenFeignVersion}")

    compileOnly("org.projectlombok:lombok")
    annotationProcessor("org.projectlombok:lombok")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation("org.springframework:spring-webflux")
    testImplementation("org.springframework.boot:spring-boot-starter-test")

    testImplementation("org.springframework.boot:spring-boot-starter-web")
    testImplementation("org.springframework.boot:spring-boot-starter-webclient")
    testImplementation("org.springframework.boot:spring-boot-starter-restclient")
    testImplementation("org.springframework.boot:spring-boot-starter-validation")
    testImplementation("org.springframework.cloud:spring-cloud-context:${springCloudCommonsVersion}")
    testImplementation("org.springframework.cloud:spring-cloud-starter-openfeign:${springCloudOpenFeignVersion}")
}

apply from: "${rootDir}/gradle/deploy.gradle"
