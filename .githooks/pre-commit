#!/usr/bin/env bash

set -e

./gradlew spotlessApply > /dev/null

# generate configuration properties docs
./gradlew classes generateConfigurationPropertiesDocs > /dev/null

# escape {} in md
perl -pi -e 's/{/\\{/g' build/configuration-properties.md
# add sidebar_position
perl -pi -e 'print "---\nsidebar_position: 40\n---\n\n" if $. == 1' build/configuration-properties.md
# remove generated time
perl -pi -e 's/This is a generated file.*\n//g' build/configuration-properties.md

cp -f build/configuration-properties.md website/docs/40-configuration-properties.md

git add -u
