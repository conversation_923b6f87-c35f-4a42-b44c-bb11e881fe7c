group=io.github.danielliu1123
version=4.0.0-SNAPSHOT

# https://github.com/spring-projects/spring-boot
springBootVersion=4.0.0-M2
# https://docs.spring.io/spring-cloud-release/reference/index.html
# https://central.sonatype.com/artifact/org.springframework.cloud/spring-cloud-dependencies
#springCloudVersion=2025.0.0
springCloudCommonsVersion=4.3.0
# https://central.sonatype.com/artifact/org.springframework.cloud/spring-cloud-dependencies
springCloudOpenFeignVersion=4.3.0
# https://github.com/spring-gradle-plugins/dependency-management-plugin
springDependencyManagementVersion=1.1.7

# https://github.com/rodnansol/spring-configuration-property-documenter
springConfigurationPropertyDocumenterVersion=0.7.1

# Code quality
# https://plugins.gradle.org/plugin/com.diffplug.gradle.spotless
spotlessVersion=7.2.1
# https://plugins.gradle.org/plugin/com.github.spotbugs
spotbugsVersion=6.1.13
# https://github.com/spotbugs/spotbugs-gradle-plugin/blob/master/build.gradle.kts
spotbugsAnnotationsVersion=4.8.6

# https://github.com/graalvm/native-build-tools
graalvmBuildToolsVersion=0.10.6

# Publishing
# https://github.com/jreleaser/jreleaser
jReleaserVersion=1.19.0

org.gradle.jvmargs=-Xmx4g
org.gradle.parallel=true
org.gradle.caching=true
